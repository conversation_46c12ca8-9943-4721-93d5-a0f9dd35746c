// lib/main.dart
import 'package:firebase_core/firebase_core.dart';
import 'package:flame/flame.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:brainy_bunny/app.dart';
import 'package:brainy_bunny/firebase_options.dart';
import 'package:brainy_bunny/services/auth_service.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    if (kDebugMode) {
      print('🚀 Starting Brainy Bunny app initialization...');
    }

    // Initialize Firebase with named instance
    await Firebase.initializeApp(
      name: 'brainy_bunny',
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Set named app as default instance for convenience
    Firebase.app('brainy_bunny').setAutomaticDataCollectionEnabled(true);

    if (kDebugMode) {
      print('✅ Firebase initialized successfully');
    }

    // Initialize services
    await AuthService.instance.initialize();

    if (kDebugMode) {
      print('✅ AuthService initialized');
    }

    // CRITICAL: Pre-initialize purchase manager and check existing purchases
    try {
      final purchaseManager = PurchaseManager();
      await purchaseManager.initialize();

      if (kDebugMode) {
        print('✅ Purchase manager initialized in main');
        print('   - Is initialized: ${purchaseManager.isInitialized}');
        print('   - Is purchased: ${purchaseManager.isPurchased}');
        print('   - Platform: ${kIsWeb ? "Web" : "Native"}');

        if (purchaseManager.isPurchased) {
          print('🎉 Found existing purchase - games will be unlocked');
        } else {
          print('🔒 No existing purchase found - premium games locked');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error initializing purchase manager in main: $e');
        print('   This is expected on web platform or when IAP is not available');
      }
    }

    // FIXED: Allow portrait orientation temporarily for system dialogs
    // Configure device orientation - allow all orientations initially
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    if (kDebugMode) {
      print('✅ Device orientation set to allow system dialogs');
    }

    // Set up Flame for fullscreen but allow orientation changes
    Flame.device.fullScreen();
    // DON'T force landscape here - let the app handle it per screen

    if (kDebugMode) {
      print('✅ Flame configured for fullscreen');
      print('🎮 Starting Brainy Bunny app...');
    }

    runApp(const BrainyBunnyApp());

  } catch (e, stackTrace) {
    if (kDebugMode) {
      print('❌ Critical error during app initialization: $e');
      print('Stack trace: $stackTrace');
    }

    // Try to run app anyway with minimal setup
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text(
                  'App initialization failed',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Error: $e',
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Restart the app
                    main();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}