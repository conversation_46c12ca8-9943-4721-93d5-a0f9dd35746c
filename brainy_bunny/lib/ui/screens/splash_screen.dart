import 'package:flutter/material.dart';
import 'package:brainy_bunny/ui/screens/home_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _flashController;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _flashAnimation;

  @override
  void initState() {
    super.initState();

    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Flash animation controller
    _flashController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Logo fade in animation
    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeInOut,
    ));

    // Logo scale animation
    _logoScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Flash animation
    _flashAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flashController,
      curve: Curves.easeInOut,
    ));

    _startSplashSequence();
  }

  Future<void> _startSplashSequence() async {
    // Wait a moment, then show logo
    await Future.delayed(const Duration(milliseconds: 300));

    // Start logo animation
    _logoController.forward();

    // Wait for logo to be fully visible
    await Future.delayed(const Duration(milliseconds: 2000));

    // Flash effect
    _flashController.forward();
    await Future.delayed(const Duration(milliseconds: 300));

    // Navigate to home screen
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const HomeScreen(),
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _flashController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: AnimatedBuilder(
        animation: Listenable.merge([_logoController, _flashController]),
        builder: (context, child) {
          return Stack(
            children: [
              // Main logo
              Center(
                child: Transform.scale(
                  scale: _logoScaleAnimation.value,
                  child: Opacity(
                    opacity: _logoOpacityAnimation.value,
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.9,
                      height: MediaQuery.of(context).size.height * 0.6,
                      child: Image.asset(
                        'assets/images/good-karma-lab-logo.jpeg',
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
              ),

              // Flash overlay
              if (_flashAnimation.value > 0)
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.white.withOpacity(_flashAnimation.value),
                ),
            ],
          );
        },
      ),
    );
  }
}
