// lib/services/app_initialization_service.dart
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/firebase_options.dart';
import 'package:brainy_bunny/services/auth_service.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';

/// Service to handle all app initialization in the background
/// This prevents blocking the main thread and showing white screen
class AppInitializationService {
  static final AppInitializationService _instance = AppInitializationService._internal();
  static AppInitializationService get instance => _instance;
  
  AppInitializationService._internal();

  bool _isInitialized = false;
  bool _isInitializing = false;
  String? _initializationError;
  
  bool get isInitialized => _isInitialized;
  bool get isInitializing => _isInitializing;
  String? get initializationError => _initializationError;

  /// Initialize all app services in the background
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    if (_isInitializing) {
      // Wait for ongoing initialization
      while (_isInitializing && !_isInitialized) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return _isInitialized;
    }

    _isInitializing = true;
    _initializationError = null;

    try {
      if (kDebugMode) {
        print('🚀 Starting background app initialization...');
      }

      // Step 1: Initialize Firebase
      await _initializeFirebase();

      // Step 2: Initialize AuthService
      await _initializeAuthService();

      // Step 3: Initialize PurchaseManager
      await _initializePurchaseManager();

      _isInitialized = true;
      _isInitializing = false;

      if (kDebugMode) {
        print('✅ All services initialized successfully');
      }

      return true;

    } catch (e, stackTrace) {
      _initializationError = e.toString();
      _isInitializing = false;
      
      if (kDebugMode) {
        print('❌ App initialization failed: $e');
        print('Stack trace: $stackTrace');
      }

      return false;
    }
  }

  /// Initialize Firebase
  Future<void> _initializeFirebase() async {
    try {
      if (kDebugMode) {
        print('🔥 Initializing Firebase...');
      }

      await Firebase.initializeApp(
        name: 'brainy_bunny',
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Set named app as default instance for convenience
      Firebase.app('brainy_bunny').setAutomaticDataCollectionEnabled(true);

      if (kDebugMode) {
        print('✅ Firebase initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firebase initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Initialize AuthService
  Future<void> _initializeAuthService() async {
    try {
      if (kDebugMode) {
        print('🔐 Initializing AuthService...');
      }

      await AuthService.instance.initialize();

      if (kDebugMode) {
        print('✅ AuthService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ AuthService initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Initialize PurchaseManager
  Future<void> _initializePurchaseManager() async {
    try {
      if (kDebugMode) {
        print('💰 Initializing PurchaseManager...');
      }

      final purchaseManager = PurchaseManager();
      await purchaseManager.initialize();

      if (kDebugMode) {
        print('✅ Purchase manager initialized');
        print('   - Is initialized: ${purchaseManager.isInitialized}');
        print('   - Is purchased: ${purchaseManager.isPurchased}');
        print('   - Platform: ${kIsWeb ? "Web" : "Native"}');

        if (purchaseManager.isPurchased) {
          print('🎉 Found existing purchase - games will be unlocked');
        } else {
          print('🔒 No existing purchase found - premium games locked');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ PurchaseManager initialization failed: $e');
        print('   This is expected on web platform or when IAP is not available');
      }
      // Don't rethrow - purchase manager failure shouldn't block app launch
    }
  }

  /// Reset initialization state (for testing or retry scenarios)
  void reset() {
    _isInitialized = false;
    _isInitializing = false;
    _initializationError = null;
  }
}
