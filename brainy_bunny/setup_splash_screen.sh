#!/bin/bash

# Setup script for optimized splash screen
echo "🚀 Setting up optimized splash screen for Brainy Bunny..."

# Step 1: Get dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

# Step 2: Generate native splash screen
echo "🎨 Generating native splash screen..."
dart run flutter_native_splash:create

# Step 3: Clean and rebuild
echo "🧹 Cleaning build cache..."
flutter clean

echo "🔨 Getting dependencies again..."
flutter pub get

echo "✅ Setup complete!"
echo ""
echo "📱 What was fixed:"
echo "   • Removed blocking initialization from main()"
echo "   • Added background initialization service"
echo "   • Removed hardcoded delays from splash screen"
echo "   • Added native splash screen support"
echo "   • Added proper error handling"
echo ""
echo "🧪 To test:"
echo "   • Run: flutter run"
echo "   • The app should show immediately without white screen"
echo "   • Initialization happens in background during splash"
echo ""
echo "📋 For App Store submission:"
echo "   • Build release version: flutter build ios --release"
echo "   • Test on physical device to ensure no white screen"
